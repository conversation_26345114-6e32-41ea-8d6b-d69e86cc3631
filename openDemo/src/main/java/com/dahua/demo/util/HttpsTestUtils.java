package com.dahua.demo.util;

import com.dahua.demo.login.Login;
import com.google.gson.Gson;
import org.apache.http.client.methods.*;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;

import javax.net.ssl.SSLContext;
import java.io.*;
import java.security.cert.X509Certificate;
import java.util.List;
import java.util.Map;

/**
 * HTTPS版本的HTTP工具类
 * 基于原始HttpTestUtils，但支持HTTPS协议
 */
public class HttpsTestUtils {

    /**
     * 发送HTTPS请求
     */
    public static String httpsRequest(HttpEnum method, String ip, int port, String action, String token, String content) {
        String responJson = null;
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        InputStream inputStream = null;
        try {
            // 创建支持HTTPS的HttpClient
            httpClient = createSSLHttpClient();
            String uri = "https://" + ip + ":" + port + action;  // 使用HTTPS
            HttpRequestBase httpReq = getRequestEntity(method, token, uri, content);
            httpResponse = httpClient.execute(httpReq);
            inputStream = httpResponse.getEntity().getContent();
            responJson = convertToString(inputStream);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != httpResponse) {
                try {
                    httpResponse.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (null != httpClient) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return responJson;
    }

    /**
     * 创建支持HTTPS的HttpClient，忽略SSL证书验证
     */
    private static CloseableHttpClient createSSLHttpClient() throws Exception {
        SSLContext sslContext = SSLContextBuilder.create()
                .loadTrustMaterial((X509Certificate[] chain, String authType) -> true)
                .build();
        
        SSLConnectionSocketFactory sslConnectionSocketFactory = new SSLConnectionSocketFactory(
                sslContext, 
                NoopHostnameVerifier.INSTANCE);
        
        return HttpClients.custom()
                .setSSLSocketFactory(sslConnectionSocketFactory)
                .build();
    }

    /**
     * 构建HTTP请求实体 - 复制自原始HttpTestUtils
     */
    private static HttpRequestBase getRequestEntity(HttpEnum method, String token, String uri, String content) throws UnsupportedEncodingException {
        switch(method.getNum()){
            case 1:
                HttpGet httpGet = new HttpGet(uri+content);
                httpGet.addHeader("Content-type", "application/json");
                httpGet.addHeader("X-Subject-Token", token);
                return httpGet;
            case 2:
                HttpPost httpPost = new HttpPost(uri);
                httpPost.addHeader("Content-type", "application/json");
                httpPost.addHeader("X-Subject-Token", token);
                StringEntity stringEntity = new StringEntity(content, "UTF-8");
                httpPost.setEntity(stringEntity);
                return httpPost;
            case 3:
                HttpPut httpPut = new HttpPut(uri);
                httpPut.addHeader("Content-type", "application/json");
                httpPut.addHeader("X-Subject-Token", token);
                StringEntity stringEntityPut = new StringEntity(content, "UTF-8");
                httpPut.setEntity(stringEntityPut);
                return httpPut;
            case 4:
                HttpDelete httpDelete = new HttpDelete(uri+content);
                httpDelete.addHeader("Content-type", "application/json");
                httpDelete.addHeader("X-Subject-Token", token);
                return httpDelete;
            default:
                return null;
        }
    }

    /**
     * 将InputStream转换为String - 复制自原始HttpTestUtils
     */
    private static String convertToString(InputStream inputStream) {
        StringBuilder stringBuilder = new StringBuilder();
        BufferedReader bufferedReader = null;
        try {
            bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                stringBuilder.append(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 获取HTTPS Token - 基于原始HttpTestUtils.getToken方法
     */
    public static String getHttpsToken(String ip, int port, String userName, String password) throws Exception {
        String response = "";
        String token = "";
        response = Login.login(ip, port, userName, password);
        Map<String, Object> rsp = new Gson().fromJson(response, Map.class);
        String message = (String) rsp.get("message");
        if ("success".equals(message)) {
            Map<String, Object> params = (Map<String, Object>) rsp.get("params");
            token = (String) params.get("token");
        } else {
            throw new Exception("登录失败: " + message);
        }
        return token;
    }
}
