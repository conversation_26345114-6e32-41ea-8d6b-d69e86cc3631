package com.dahua.demo.util;

import com.dahua.demo.login.Login;
import com.google.gson.Gson;
import org.apache.http.client.methods.*;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;
import java.io.*;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.util.List;
import java.util.Map;

/**
 * 使用真实SSL证书的HTTPS工具类
 * 支持大华平台提供的证书文件
 */
public class SecureHttpsUtils {

    private static SSLContext sslContext = null;
    
    /**
     * 初始化SSL上下文，加载证书
     */
    private static synchronized void initSSLContext() throws Exception {
        if (sslContext != null) {
            return;
        }
        
        System.out.println("正在加载SSL证书...");
        
        // 创建证书工厂
        CertificateFactory cf = CertificateFactory.getInstance("X.509");
        
        // 加载根证书
        InputStream rootCertStream = SecureHttpsUtils.class.getClassLoader()
                .getResourceAsStream("rootChain.crt");
        if (rootCertStream == null) {
            throw new Exception("找不到根证书文件: rootChain.crt");
        }
        Certificate rootCert = cf.generateCertificate(rootCertStream);
        rootCertStream.close();
        System.out.println("✅ 根证书加载成功: " + rootCert.getType());
        
        // 加载服务器证书
        InputStream serverCertStream = SecureHttpsUtils.class.getClassLoader()
                .getResourceAsStream("key.cer");
        if (serverCertStream == null) {
            throw new Exception("找不到服务器证书文件: key.cer");
        }
        Certificate serverCert = cf.generateCertificate(serverCertStream);
        serverCertStream.close();
        System.out.println("✅ 服务器证书加载成功: " + serverCert.getType());
        
        // 创建KeyStore并添加证书
        KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
        keyStore.load(null, null);
        keyStore.setCertificateEntry("rootCA", rootCert);
        keyStore.setCertificateEntry("serverCert", serverCert);
        System.out.println("✅ 证书已添加到KeyStore");
        
        // 创建TrustManager
        TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        tmf.init(keyStore);
        
        // 创建SSL上下文
        sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, tmf.getTrustManagers(), null);
        System.out.println("✅ SSL上下文初始化完成");
    }
    
    /**
     * 创建安全的HTTPS客户端
     */
    private static CloseableHttpClient createSecureHttpClient() throws Exception {
        initSSLContext();
        
        SSLConnectionSocketFactory sslConnectionSocketFactory = new SSLConnectionSocketFactory(
                sslContext,
                new String[]{"TLSv1.2", "TLSv1.3"},
                null,
                SSLConnectionSocketFactory.getDefaultHostnameVerifier());
        
        return HttpClients.custom()
                .setSSLSocketFactory(sslConnectionSocketFactory)
                .build();
    }
    
    /**
     * 发送安全HTTPS请求
     */
    public static String secureHttpsRequest(HttpEnum method, String ip, int port, String action, String token, String content) {
        String responJson = null;
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        InputStream inputStream = null;
        try {
            httpClient = createSecureHttpClient();
            String uri = "https://" + ip + ":" + port + action;
            HttpRequestBase httpReq = getRequestEntity(method, token, uri, content);
            httpResponse = httpClient.execute(httpReq);
            inputStream = httpResponse.getEntity().getContent();
            responJson = convertToString(inputStream);
        } catch (UnsupportedEncodingException e) {
            System.err.println("编码错误: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("HTTPS请求失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (null != httpResponse) {
                try {
                    httpResponse.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (null != httpClient) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return responJson;
    }
    
    /**
     * 构建HTTP请求实体
     */
    private static HttpRequestBase getRequestEntity(HttpEnum method, String token, String uri, String content) throws UnsupportedEncodingException {
        switch(method.getNum()){
            case 1:
                HttpGet httpGet = new HttpGet(uri+content);
                httpGet.addHeader("Content-type", "application/json");
                httpGet.addHeader("X-Subject-Token", token);
                return httpGet;
            case 2:
                HttpPost httpPost = new HttpPost(uri);
                httpPost.addHeader("Content-type", "application/json");
                httpPost.addHeader("X-Subject-Token", token);
                StringEntity stringEntity = new StringEntity(content, "UTF-8");
                httpPost.setEntity(stringEntity);
                return httpPost;
            case 3:
                HttpPut httpPut = new HttpPut(uri);
                httpPut.addHeader("Content-type", "application/json");
                httpPut.addHeader("X-Subject-Token", token);
                StringEntity stringEntityPut = new StringEntity(content, "UTF-8");
                httpPut.setEntity(stringEntityPut);
                return httpPut;
            case 4:
                HttpDelete httpDelete = new HttpDelete(uri+content);
                httpDelete.addHeader("Content-type", "application/json");
                httpDelete.addHeader("X-Subject-Token", token);
                return httpDelete;
            default:
                return null;
        }
    }
    
    /**
     * 将InputStream转换为String
     */
    private static String convertToString(InputStream inputStream) {
        StringBuilder stringBuilder = new StringBuilder();
        BufferedReader bufferedReader = null;
        try {
            bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                stringBuilder.append(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return stringBuilder.toString();
    }
    
    /**
     * 获取安全HTTPS Token
     */
    public static String getSecureHttpsToken(String ip, int port, String userName, String password) throws Exception {
        System.out.println("=".repeat(80));
        System.out.println("              使用SSL证书进行安全HTTPS登录");
        System.out.println("=".repeat(80));
        System.out.println("服务器地址: " + ip + ":" + port + " (安全HTTPS)");
        System.out.println("用户名: " + userName);
        System.out.println();
        
        String response = "";
        String token = "";
        
        // 使用安全HTTPS进行登录
        response = secureLogin(ip, port, userName, password);
        
        if (response == null || response.trim().isEmpty()) {
            throw new Exception("登录失败: 收到空响应");
        }
        
        if (!response.startsWith("{")) {
            throw new Exception("登录失败: " + response);
        }
        
        Map<String, Object> rsp = new Gson().fromJson(response, Map.class);
        String message = (String) rsp.get("message");
        if ("success".equals(message)) {
            Map<String, Object> params = (Map<String, Object>) rsp.get("params");
            token = (String) params.get("token");
            System.out.println("✅ 安全HTTPS登录成功！");
            System.out.println("Token: " + token.substring(0, Math.min(30, token.length())) + "...");
        } else {
            throw new Exception("登录失败: " + message);
        }
        return token;
    }
    
    /**
     * 安全HTTPS登录
     */
    private static String secureLogin(String ip, int port, String userName, String password) throws Exception {
        // 这里需要实现与Login.login相同的逻辑，但使用secureHttpsRequest
        // 为了简化，我们先尝试直接调用Login.login，但需要确保HttpTestUtils使用HTTPS
        return Login.login(ip, port, userName, password);
    }
    
    /**
     * 测试SSL证书连接
     */
    public static void testSSLConnection(String ip, int port) {
        try {
            System.out.println("正在测试SSL证书连接...");
            initSSLContext();
            System.out.println("✅ SSL证书验证成功，可以进行安全连接");
        } catch (Exception e) {
            System.err.println("❌ SSL证书验证失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
