package com.dahua.demo.basic;

import com.dahua.demo.util.BaseUserInfo;

import java.util.List;

/**
 * 组织和通道信息获取示例
 * 演示如何使用OrganizationChannelService获取和显示组织通道信息
 */
public class OrganizationChannelExample extends BaseUserInfo {
    
    /**
     * 示例1：基本使用方法
     */
    public static void basicUsageExample() {
        System.out.println("=== 示例1：基本使用方法 ===");
        
        try {
            // 创建服务实例
            OrganizationChannelService service = new OrganizationChannelService();
            
            // 获取所有组织和通道信息
            List<Organization> organizations = service.getAllOrganizationsWithChannels();
            
            // 打印结构化信息
            service.printOrganizationTree(organizations);
            
        } catch (Exception e) {
            System.err.println("获取组织通道信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 示例2：自定义处理组织和通道数据
     */
    public static void customProcessingExample() {
        System.out.println("\n=== 示例2：自定义处理组织和通道数据 ===");
        
        try {
            OrganizationChannelService service = new OrganizationChannelService();
            List<Organization> organizations = service.getAllOrganizationsWithChannels();
            
            // 自定义处理逻辑
            processOrganizations(organizations);
            
        } catch (Exception e) {
            System.err.println("处理组织通道信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 递归处理组织数据的示例方法
     */
    private static void processOrganizations(List<Organization> organizations) {
        for (Organization org : organizations) {
            // 处理当前组织
            System.out.println("处理组织: " + org.getOrgName() + " (ID: " + org.getId() + ")");
            
            // 处理组织下的通道
            for (Channel channel : org.getChannels()) {
                System.out.println("  - 通道: " + channel.getChannelName() + " (ID: " + channel.getChannelId() + ")");
                
                // 在这里可以添加自定义的通道处理逻辑
                // 例如：检查通道状态、获取实时视频流等
            }
            
            // 递归处理子组织
            if (!org.getSubOrganizations().isEmpty()) {
                System.out.println("  处理子组织...");
                processOrganizations(org.getSubOrganizations());
            }
        }
    }
    
    /**
     * 示例3：统计信息示例
     */
    public static void statisticsExample() {
        System.out.println("\n=== 示例3：统计信息示例 ===");
        
        try {
            OrganizationChannelService service = new OrganizationChannelService();
            List<Organization> organizations = service.getAllOrganizationsWithChannels();
            
            // 计算统计信息
            int totalOrgs = countOrganizations(organizations);
            int totalChannels = countChannels(organizations);
            int maxDepth = calculateMaxDepth(organizations, 0);
            
            System.out.println("统计信息:");
            System.out.println("- 总组织数: " + totalOrgs);
            System.out.println("- 总通道数: " + totalChannels);
            System.out.println("- 最大层级深度: " + maxDepth);
            System.out.println("- 平均每组织通道数: " + (totalOrgs > 0 ? (double)totalChannels / totalOrgs : 0));
            
        } catch (Exception e) {
            System.err.println("统计信息计算失败: " + e.getMessage());
        }
    }
    
    private static int countOrganizations(List<Organization> organizations) {
        int count = organizations.size();
        for (Organization org : organizations) {
            count += countOrganizations(org.getSubOrganizations());
        }
        return count;
    }
    
    private static int countChannels(List<Organization> organizations) {
        int count = 0;
        for (Organization org : organizations) {
            count += org.getChannels().size();
            count += countChannels(org.getSubOrganizations());
        }
        return count;
    }
    
    private static int calculateMaxDepth(List<Organization> organizations, int currentDepth) {
        int maxDepth = currentDepth;
        for (Organization org : organizations) {
            int subDepth = calculateMaxDepth(org.getSubOrganizations(), currentDepth + 1);
            maxDepth = Math.max(maxDepth, subDepth);
        }
        return maxDepth;
    }
    
    /**
     * 示例4：错误处理示例
     */
    public static void errorHandlingExample() {
        System.out.println("\n=== 示例4：错误处理示例 ===");
        
        try {
            // 检查token是否有效
            if (token == null || token.trim().isEmpty()) {
                System.err.println("错误: Token无效，请先登录");
                System.err.println("解决方案: 运行 com.dahua.demo.login.Login 类进行登录");
                return;
            }
            
            // 检查网络连接
            System.out.println("检查服务器连接: " + ip + ":" + port);
            
            OrganizationChannelService service = new OrganizationChannelService();
            List<Organization> organizations = service.getAllOrganizationsWithChannels();
            
            if (organizations.isEmpty()) {
                System.out.println("警告: 未找到任何组织信息");
                System.out.println("可能原因:");
                System.out.println("1. 用户权限不足");
                System.out.println("2. 系统中确实没有组织数据");
                System.out.println("3. API接口返回格式发生变化");
            } else {
                System.out.println("成功获取到 " + organizations.size() + " 个根组织");
            }
            
        } catch (Exception e) {
            System.err.println("执行过程中发生错误: " + e.getMessage());
            
            // 提供详细的错误处理建议
            System.err.println("\n错误处理建议:");
            if (e.getMessage().contains("Connection")) {
                System.err.println("- 检查网络连接");
                System.err.println("- 确认服务器地址和端口正确");
            } else if (e.getMessage().contains("token") || e.getMessage().contains("401")) {
                System.err.println("- Token可能已过期，请重新登录");
            } else if (e.getMessage().contains("403")) {
                System.err.println("- 用户权限不足，请联系管理员");
            } else {
                System.err.println("- 查看详细错误信息进行排查");
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 主方法 - 运行所有示例
     */
    public static void main(String[] args) {
        System.out.println("组织和通道信息获取工具 - 使用示例");
        System.out.println("=".repeat(60));
        
        // 运行各种示例
        basicUsageExample();
        customProcessingExample();
        statisticsExample();
        errorHandlingExample();
        
        System.out.println("\n所有示例执行完成！");
    }
}
