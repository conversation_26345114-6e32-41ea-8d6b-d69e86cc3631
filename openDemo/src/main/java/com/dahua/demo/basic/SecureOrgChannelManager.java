package com.dahua.demo.basic;

import com.dahua.demo.util.BaseUserInfo;
import com.dahua.demo.util.SecureHttpsUtils;
import com.dahua.demo.util.HttpEnum;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 使用真实SSL证书的安全组织通道管理器
 * 基于大华平台提供的证书文件进行安全HTTPS通信
 */
public class SecureOrgChannelManager extends BaseUserInfo {
    
    private static final String ORG_TREE_ACTION = "/videoService/devicesManager/deviceTree";
    
    /**
     * 获取所有组织和通道信息
     */
    public static void getAllOrganizationsWithChannels() {
        try {
            System.out.println("=".repeat(80));
            System.out.println("          大华开放平台 - 安全SSL证书组织通道查询");
            System.out.println("=".repeat(80));
            System.out.println("服务器地址: " + ip + ":" + port + " (安全HTTPS)");
            System.out.println();
            
            // 测试SSL证书连接
            SecureHttpsUtils.testSSLConnection(ip, Integer.valueOf(port));
            System.out.println();
            
            // 获取安全Token
            System.out.println("步骤1: 获取访问Token...");
            String token = SecureHttpsUtils.getSecureHttpsToken(ip, Integer.valueOf(port), userName, password);
            System.out.println("Token长度: " + token.length() + " 字符");
            System.out.println();
            
            // 查询组织树
            System.out.println("步骤2: 查询组织树结构...");
            loadOrganizationTree("", 1, token);
            
            System.out.println();
            System.out.println("=".repeat(80));
            System.out.println("🎉 安全HTTPS查询完成！");
            System.out.println("=".repeat(80));
            
        } catch (Exception e) {
            System.err.println("\n❌ 查询失败: " + e.getMessage());
            System.err.println("\n错误处理建议:");
            System.err.println("1. 检查SSL证书文件是否存在且有效");
            System.err.println("2. 确认服务器支持提供的证书");
            System.err.println("3. 检查用户名和密码");
            System.err.println("4. 确认网络连接正常");
            e.printStackTrace();
        }
    }
    
    /**
     * 递归加载组织树
     */
    private static void loadOrganizationTree(String parentId, int level, String token) {
        try {
            // 构建查询参数
            String queryParams = "?id=" + parentId + "&nodeType=1&typeCode=01&page=1&pageSize=100";
            
            // 发送安全HTTPS请求
            String response = SecureHttpsUtils.secureHttpsRequest(
                HttpEnum.GET, ip, Integer.valueOf(port), ORG_TREE_ACTION, token, queryParams);
            
            if (response == null || response.trim().isEmpty()) {
                System.err.println("收到空响应，可能Token已过期");
                return;
            }
            
            if (!response.startsWith("{")) {
                System.err.println("收到非JSON响应: " + response);
                return;
            }
            
            // 解析响应
            Map<String, Object> responseMap = new Gson().fromJson(response, Map.class);
            String message = (String) responseMap.get("message");
            
            if (!"success".equals(message)) {
                System.err.println("API调用失败: " + message);
                return;
            }
            
            Map<String, Object> params = (Map<String, Object>) responseMap.get("params");
            if (params == null) {
                System.out.println("  " + "  ".repeat(level - 1) + "└─ (无子节点)");
                return;
            }
            
            List<Map<String, Object>> items = (List<Map<String, Object>>) params.get("items");
            if (items == null || items.isEmpty()) {
                if (level == 1) {
                    System.out.println("  └─ (未找到组织节点)");
                }
                return;
            }
            
            // 显示当前层级的节点
            for (int i = 0; i < items.size(); i++) {
                Map<String, Object> item = items.get(i);
                String id = (String) item.get("id");
                String name = (String) item.get("name");
                String nodeType = String.valueOf(item.get("nodeType"));
                String typeCode = (String) item.get("typeCode");
                
                // 构建树形显示
                String prefix = "  ".repeat(level - 1);
                String connector = (i == items.size() - 1) ? "└─" : "├─";
                
                // 根据节点类型显示不同图标
                String icon = getNodeIcon(nodeType, typeCode);
                
                System.out.println(prefix + connector + " " + icon + " " + name + 
                    " [ID:" + id + ", 类型:" + nodeType + ", 代码:" + typeCode + "]");
                
                // 如果是组织节点，递归查询子节点
                if ("1".equals(nodeType)) {
                    loadOrganizationTree(id, level + 1, token);
                }
            }
            
        } catch (Exception e) {
            System.err.println("  " + "  ".repeat(level - 1) + "└─ ❌ 查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据节点类型获取显示图标
     */
    private static String getNodeIcon(String nodeType, String typeCode) {
        if ("1".equals(nodeType)) {
            return "📁"; // 组织节点
        } else if ("2".equals(nodeType)) {
            if ("01".equals(typeCode)) {
                return "📹"; // 摄像头
            } else if ("02".equals(typeCode)) {
                return "🎤"; // 音频设备
            } else {
                return "📱"; // 其他设备
            }
        }
        return "❓"; // 未知类型
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        System.out.println("启动安全SSL证书组织通道管理器...");
        System.out.println("使用证书文件:");
        System.out.println("  - 根证书: rootChain.crt");
        System.out.println("  - 服务器证书: key.cer");
        System.out.println();
        
        getAllOrganizationsWithChannels();
    }
}
