package com.dahua.demo.basic;

import com.dahua.demo.util.BaseUserInfo;
import com.dahua.demo.util.HttpEnum;
import com.dahua.demo.util.HttpTestUtils;
import com.google.gson.Gson;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 组织和通道信息服务类
 * 提供获取组织树和通道信息的核心业务逻辑
 */
public class OrganizationChannelService extends BaseUserInfo {
    
    private static final String ACTION = "/videoService/devicesManager/deviceTree";
    private static final String ORG_TYPE_CODE = "01";
    private static final String CHANNEL_TYPE_CODE = "01;0;ALL;ALL";
    private static final int CHANNEL_NODE_TYPE = 3;
    private static final int ORG_NODE_TYPE = 1;
    
    private Gson gson;
    
    public OrganizationChannelService() {
        this.gson = new Gson();
    }
    
    /**
     * 获取所有组织及其通道信息
     * @return 根组织列表
     * @throws Exception 当API调用失败时抛出异常
     */
    public List<Organization> getAllOrganizationsWithChannels() throws Exception {
        System.out.println("开始获取组织和通道信息...");
        
        // 首先获取根组织
        List<Organization> rootOrganizations = getOrganizations("");
        
        // 递归获取每个根组织的子组织和通道
        for (Organization rootOrg : rootOrganizations) {
            loadOrganizationTree(rootOrg, 0);
        }
        
        System.out.println("组织和通道信息获取完成！");
        return rootOrganizations;
    }
    
    /**
     * 递归加载组织树结构和通道信息
     * @param organization 当前组织
     * @param level 组织层级
     * @throws Exception 当API调用失败时抛出异常
     */
    private void loadOrganizationTree(Organization organization, int level) throws Exception {
        organization.setLevel(level);
        
        // 获取当前组织下的通道
        List<Channel> channels = getChannels(organization.getId());
        organization.setChannels(channels);
        
        // 获取当前组织下的子组织
        List<Organization> subOrganizations = getOrganizations(organization.getId());
        organization.setSubOrganizations(subOrganizations);
        
        // 递归处理子组织
        for (Organization subOrg : subOrganizations) {
            subOrg.setParentId(organization.getId());
            loadOrganizationTree(subOrg, level + 1);
        }
    }
    
    /**
     * 获取指定父组织下的子组织列表
     * @param parentId 父组织ID，空字符串表示根组织
     * @return 组织列表
     * @throws Exception 当API调用失败时抛出异常
     */
    private List<Organization> getOrganizations(String parentId) throws Exception {
        List<Organization> organizations = new ArrayList<>();
        
        try {
            String content = buildOrgQueryParams(parentId);
            String response = HttpTestUtils.httpRequest(HttpEnum.GET, ip, Integer.valueOf(port), ACTION, token, content);
            
            if (response == null || response.trim().isEmpty()) {
                System.err.println("获取组织信息时收到空响应");
                return organizations;
            }
            
            Map<String, Object> responseMap = gson.fromJson(response, Map.class);
            List<Map<String, Object>> results = (List<Map<String, Object>>) responseMap.get("results");
            
            if (results != null && !results.isEmpty()) {
                for (Map<String, Object> item : results) {
                    Organization org = mapToOrganization(item);
                    if (org != null) {
                        organizations.add(org);
                    }
                }
            }
            
        } catch (Exception e) {
            System.err.println("获取组织信息时发生错误: " + e.getMessage());
            throw new Exception("获取组织信息失败: " + e.getMessage(), e);
        }
        
        return organizations;
    }
    
    /**
     * 获取指定组织下的通道列表
     * @param orgId 组织ID
     * @return 通道列表
     * @throws Exception 当API调用失败时抛出异常
     */
    private List<Channel> getChannels(String orgId) throws Exception {
        List<Channel> channels = new ArrayList<>();
        
        try {
            String content = buildChannelQueryParams(orgId);
            String response = HttpTestUtils.httpRequest(HttpEnum.GET, ip, Integer.valueOf(port), ACTION, token, content);
            
            if (response == null || response.trim().isEmpty()) {
                return channels; // 返回空列表，不抛出异常
            }
            
            Map<String, Object> responseMap = gson.fromJson(response, Map.class);
            List<Map<String, Object>> results = (List<Map<String, Object>>) responseMap.get("results");
            
            if (results != null && !results.isEmpty()) {
                DecimalFormat df = new DecimalFormat("######0");
                for (Map<String, Object> item : results) {
                    // 只处理通道类型的节点
                    if (CHANNEL_NODE_TYPE == Integer.valueOf(df.format(item.get("nodeType")))) {
                        Channel channel = mapToChannel(item);
                        if (channel != null) {
                            channels.add(channel);
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            System.err.println("获取通道信息时发生错误 (组织ID: " + orgId + "): " + e.getMessage());
            // 不抛出异常，返回空列表
        }
        
        return channels;
    }
    
    /**
     * 构建组织查询参数
     */
    private String buildOrgQueryParams(String parentId) {
        return "?id=" + (parentId != null ? parentId : "") +
               "&nodeType=" + ORG_NODE_TYPE +
               "&typeCode=" + ORG_TYPE_CODE +
               "&page=1&pageSize=100";
    }
    
    /**
     * 构建通道查询参数
     */
    private String buildChannelQueryParams(String orgId) {
        return "?id=" + (orgId != null ? orgId : "") +
               "&nodeType=" + ORG_NODE_TYPE +
               "&typeCode=" + CHANNEL_TYPE_CODE +
               "&page=1&pageSize=100";
    }
    
    /**
     * 将API响应映射为Organization对象
     */
    private Organization mapToOrganization(Map<String, Object> item) {
        try {
            Organization org = new Organization();
            org.setId((String) item.get("id"));
            org.setOrgName((String) item.get("orgName"));
            org.setOrgCode((String) item.get("orgCode"));
            
            if (item.get("nodeType") != null) {
                DecimalFormat df = new DecimalFormat("######0");
                org.setNodeType(Integer.valueOf(df.format(item.get("nodeType"))));
            }
            
            return org;
        } catch (Exception e) {
            System.err.println("映射组织对象时发生错误: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 将API响应映射为Channel对象
     */
    private Channel mapToChannel(Map<String, Object> item) {
        try {
            Channel channel = new Channel();
            channel.setChannelId((String) item.get("channelId"));
            channel.setChannelName((String) item.get("channelName"));
            channel.setChannelCode((String) item.get("channelCode"));
            channel.setDeviceId((String) item.get("deviceId"));
            channel.setDeviceName((String) item.get("deviceName"));

            if (item.get("nodeType") != null) {
                DecimalFormat df = new DecimalFormat("######0");
                channel.setNodeType(Integer.valueOf(df.format(item.get("nodeType"))));
            }

            return channel;
        } catch (Exception e) {
            System.err.println("映射通道对象时发生错误: " + e.getMessage());
            return null;
        }
    }

    /**
     * 打印组织和通道树结构
     * @param organizations 组织列表
     */
    public void printOrganizationTree(List<Organization> organizations) {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("                    组织和通道信息树状结构");
        System.out.println("=".repeat(80));

        if (organizations == null || organizations.isEmpty()) {
            System.out.println("未找到任何组织信息");
            return;
        }

        for (Organization org : organizations) {
            printOrganization(org, "");
        }

        System.out.println("=".repeat(80));
        printSummary(organizations);
    }

    /**
     * 递归打印组织信息
     * @param org 组织对象
     * @param prefix 前缀字符串，用于缩进
     */
    private void printOrganization(Organization org, String prefix) {
        // 打印组织信息
        System.out.println(prefix + "📁 组织: " + org.getOrgName());
        System.out.println(prefix + "   ├─ 组织ID: " + org.getId());
        System.out.println(prefix + "   ├─ 组织编码: " + (org.getOrgCode() != null ? org.getOrgCode() : "N/A"));
        System.out.println(prefix + "   ├─ 层级: " + org.getLevel());

        // 打印通道信息
        List<Channel> channels = org.getChannels();
        if (channels != null && !channels.isEmpty()) {
            System.out.println(prefix + "   ├─ 通道数量: " + channels.size());
            for (int i = 0; i < channels.size(); i++) {
                Channel channel = channels.get(i);
                boolean isLast = (i == channels.size() - 1) && (org.getSubOrganizations().isEmpty());
                String channelPrefix = isLast ? "   └─ " : "   ├─ ";

                System.out.println(prefix + channelPrefix + "📹 通道: " + channel.getChannelName());
                System.out.println(prefix + "       ├─ 通道ID: " + channel.getChannelId());
                System.out.println(prefix + "       ├─ 通道编码: " + (channel.getChannelCode() != null ? channel.getChannelCode() : "N/A"));
                System.out.println(prefix + "       └─ 设备名称: " + (channel.getDeviceName() != null ? channel.getDeviceName() : "N/A"));
            }
        } else {
            System.out.println(prefix + "   ├─ 通道数量: 0");
        }

        // 递归打印子组织
        List<Organization> subOrgs = org.getSubOrganizations();
        if (subOrgs != null && !subOrgs.isEmpty()) {
            System.out.println(prefix + "   └─ 子组织数量: " + subOrgs.size());
            for (int i = 0; i < subOrgs.size(); i++) {
                Organization subOrg = subOrgs.get(i);
                boolean isLast = (i == subOrgs.size() - 1);
                String newPrefix = prefix + (isLast ? "       " : "   │   ");
                System.out.println(prefix + (isLast ? "       └─ " : "   │   ├─ "));
                printOrganization(subOrg, newPrefix);
            }
        }

        System.out.println(); // 空行分隔
    }

    /**
     * 打印统计摘要
     * @param organizations 组织列表
     */
    private void printSummary(List<Organization> organizations) {
        int totalOrgs = countTotalOrganizations(organizations);
        int totalChannels = countTotalChannels(organizations);

        System.out.println("📊 统计摘要:");
        System.out.println("   总组织数量: " + totalOrgs);
        System.out.println("   总通道数量: " + totalChannels);
        System.out.println("   获取时间: " + new java.util.Date());
    }

    /**
     * 递归统计组织总数
     */
    private int countTotalOrganizations(List<Organization> organizations) {
        int count = 0;
        for (Organization org : organizations) {
            count++; // 当前组织
            count += countTotalOrganizations(org.getSubOrganizations()); // 子组织
        }
        return count;
    }

    /**
     * 递归统计通道总数
     */
    private int countTotalChannels(List<Organization> organizations) {
        int count = 0;
        for (Organization org : organizations) {
            count += org.getChannels().size(); // 当前组织的通道
            count += countTotalChannels(org.getSubOrganizations()); // 子组织的通道
        }
        return count;
    }
}
