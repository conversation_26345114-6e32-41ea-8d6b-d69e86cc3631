package com.dahua.demo.basic;

import com.dahua.demo.util.BaseUserInfo;
import com.dahua.demo.util.HttpTestUtils;

import java.util.List;

/**
 * 组织和通道管理器
 * 主入口类，用于演示如何获取和显示系统中的所有组织和通道信息
 * 
 * 使用说明：
 * 1. 确保已在baseinfo.properties中配置正确的服务器信息
 * 2. 确保已通过Login类获取有效的token
 * 3. 运行main方法即可获取并显示所有组织和通道信息
 */
public class OrganizationChannelManager extends BaseUserInfo {
    
    private OrganizationChannelService service;
    
    public OrganizationChannelManager() {
        this.service = new OrganizationChannelService();
    }
    
    /**
     * 执行获取和显示组织通道信息的主要逻辑
     */
    public void execute() {
        try {
            // 验证token是否有效
            if (token == null || token.trim().isEmpty()) {
                System.err.println("错误: 未找到有效的token，请先运行Login类进行登录");
                System.err.println("登录步骤:");
                System.err.println("1. 确保baseinfo.properties中配置了正确的服务器信息");
                System.err.println("2. 运行com.dahua.demo.login.Login类的main方法");
                System.err.println("3. 登录成功后再运行此程序");
                return;
            }
            
            System.out.println("开始获取组织和通道信息...");
            System.out.println("服务器地址: " + ip + ":" + port);
            System.out.println("Token: " + token.substring(0, Math.min(20, token.length())) + "...");
            System.out.println();
            
            // 获取所有组织和通道信息
            List<Organization> organizations = service.getAllOrganizationsWithChannels();
            
            // 打印结构化的组织和通道信息
            service.printOrganizationTree(organizations);
            
        } catch (Exception e) {
            System.err.println("获取组织和通道信息时发生错误: " + e.getMessage());
            e.printStackTrace();
            
            // 提供错误处理建议
            System.err.println("\n错误处理建议:");
            System.err.println("1. 检查网络连接是否正常");
            System.err.println("2. 检查服务器地址和端口是否正确");
            System.err.println("3. 检查token是否已过期，如已过期请重新登录");
            System.err.println("4. 检查用户是否有足够的权限访问组织和通道信息");
        }
    }
    
    /**
     * 验证连接和权限
     * @return 是否验证成功
     */
    public boolean validateConnection() {
        try {
            System.out.println("正在验证连接和权限...");
            
            // 尝试获取根组织信息来验证连接
            String testToken = HttpTestUtils.getToken(ip, Integer.valueOf(port), userName, password);
            if (testToken != null && !testToken.trim().isEmpty()) {
                System.out.println("连接验证成功");
                return true;
            } else {
                System.err.println("连接验证失败: 无法获取有效token");
                return false;
            }
            
        } catch (Exception e) {
            System.err.println("连接验证失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 主方法 - 程序入口点
     * 
     * 注意事项：
     * 1. 使用前请确保已在baseinfo.properties中配置正确的服务器信息
     * 2. 请确保已通过Login类获取有效的token
     * 3. 确保用户有足够的权限访问组织和通道信息
     * 
     * @param args 命令行参数（未使用）
     */
    public static void main(String[] args) {
        System.out.println("=".repeat(80));
        System.out.println("              大华开放平台 - 组织和通道信息获取工具");
        System.out.println("=".repeat(80));
        System.out.println();
        
        OrganizationChannelManager manager = new OrganizationChannelManager();
        
        // 执行主要逻辑
        manager.execute();
        
        System.out.println("\n程序执行完成。");
    }
}
