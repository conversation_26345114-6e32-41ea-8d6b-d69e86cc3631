package com.dahua.demo.basic;

import com.dahua.demo.util.BaseUserInfo;
import com.google.gson.Gson;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Map;

/**
 * 简单的HTTPS版本组织通道管理器
 * 解决"HTTP request was sent to HTTPS port"问题
 */
public class SimpleHttpsOrgManager extends BaseUserInfo {
    
    public static final String ACTION = "/videoService/devicesManager/deviceTree";
    private static int orgLevel = -1;
    private static String indentMark = "";
    
    /**
     * 发送HTTPS请求
     */
    private static String sendHttpsRequest(String queryParams) {
        try {
            // 构建HTTPS URL
            String httpsUrl = "https://" + ip + ":" + port + ACTION + queryParams;
            URL url = new URL(httpsUrl);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            
            // 忽略SSL证书验证（仅用于测试环境）
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return null; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) { }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) { }
                }
            };
            
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            connection.setSSLSocketFactory(sc.getSocketFactory());
            connection.setHostnameVerifier(new javax.net.ssl.HostnameVerifier() {
                public boolean verify(String hostname, javax.net.ssl.SSLSession session) {
                    return true;
                }
            });
            
            // 设置请求头
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-Subject-Token", token);
            connection.setConnectTimeout(10000); // 10秒连接超时
            connection.setReadTimeout(30000);    // 30秒读取超时
            
            // 读取响应
            BufferedReader reader = new BufferedReader(
                new InputStreamReader(connection.getInputStream()));
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();
            
            return response.toString();
            
        } catch (Exception e) {
            System.err.println("HTTPS请求失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 测试HTTPS连接
     */
    private static boolean testHttpsConnection() {
        try {
            System.out.println("正在测试HTTPS连接...");
            String response = sendHttpsRequest("?id=&nodeType=1&typeCode=01&page=1&pageSize=1");
            
            if (response == null || response.trim().isEmpty()) {
                System.err.println("❌ 收到空响应");
                return false;
            }
            
            if (response.contains("400") && response.contains("HTTP request was sent to HTTPS port")) {
                System.err.println("❌ 仍然是HTTP/HTTPS协议错误");
                return false;
            }
            
            if (!response.startsWith("{")) {
                System.err.println("❌ 收到非JSON响应: " + response.substring(0, Math.min(200, response.length())));
                return false;
            }
            
            System.out.println("✅ HTTPS连接测试成功");
            return true;
            
        } catch (Exception e) {
            System.err.println("❌ HTTPS连接测试失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取并打印所有组织和通道信息
     */
    public static void getAllOrganizationsWithChannels() throws Exception {
        System.out.println("开始获取组织和通道信息...");
        System.out.println("服务器地址: " + ip + ":" + port + " (HTTPS)");
        System.out.println("Token: " + (token != null ? token.substring(0, Math.min(20, token.length())) + "..." : "null"));
        System.out.println();
        
        // 检查Token
        if (token == null || token.trim().isEmpty()) {
            System.err.println("错误: Token为空，请先运行Login类进行登录");
            return;
        }
        
        // 测试HTTPS连接
        if (!testHttpsConnection()) {
            System.err.println("\nHTTPS连接失败，请检查:");
            System.err.println("1. 服务器是否支持HTTPS协议");
            System.err.println("2. 端口配置是否正确");
            System.err.println("3. Token是否有效");
            return;
        }
        
        System.out.println("\n" + "=".repeat(80));
        System.out.println("                    组织和通道信息树状结构 (HTTPS)");
        System.out.println("=".repeat(80));
        
        // 从根组织开始递归获取
        getSubOrganizations("");
        
        System.out.println("=".repeat(80));
        System.out.println("获取完成！");
    }
    
    /**
     * 递归获取子组织和通道信息
     */
    private static void getSubOrganizations(String parentOrgId) throws Exception {
        try {
            String queryParams = "?id=" + parentOrgId + "&nodeType=1&typeCode=01&page=1&pageSize=100";
            String response = sendHttpsRequest(queryParams);
            
            if (response == null || response.trim().isEmpty()) {
                System.err.println("收到空响应");
                return;
            }
            
            if (!response.startsWith("{")) {
                System.err.println("API返回错误信息: " + response);
                return;
            }
            
            Map<String, Object> responseMap = new Gson().fromJson(response, Map.class);
            List<Map<String, Object>> results = (List<Map<String, Object>>) responseMap.get("results");
            
            if (results != null && results.size() > 0) {
                for (Map<String, Object> orgNode : results) {
                    orgLevel++;
                    indentMark += "   ";
                    
                    String orgName = (String) orgNode.get("orgName");
                    String orgId = (String) orgNode.get("id");
                    System.out.println(indentMark + "📁 " + orgLevel + "级组织: " + orgName + " (ID: " + orgId + ")");
                    
                    // 获取该组织下的通道信息
                    getChannelsForOrganization(orgId, indentMark);
                    
                    // 递归获取子组织
                    getSubOrganizations(orgId);
                    
                    // 恢复缩进级别
                    indentMark = indentMark.substring(0, indentMark.length() - 3);
                    orgLevel--;
                }
            } else {
                if (parentOrgId.isEmpty()) {
                    System.out.println("未找到任何组织信息，可能是权限不足或系统中没有数据");
                }
            }
            
        } catch (Exception e) {
            System.err.println("获取组织信息时发生错误: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * 获取指定组织下的通道信息
     */
    private static void getChannelsForOrganization(String orgId, String indent) {
        try {
            String queryParams = "?id=" + orgId + "&nodeType=1&typeCode=01;0;ALL;ALL&page=1&pageSize=100";
            String response = sendHttpsRequest(queryParams);
            
            if (response == null || !response.startsWith("{")) {
                System.out.println(indent + "   └─ 通道: 获取失败");
                return;
            }
            
            Map<String, Object> responseMap = new Gson().fromJson(response, Map.class);
            List<Map<String, Object>> results = (List<Map<String, Object>>) responseMap.get("results");
            
            if (results != null && results.size() > 0) {
                int channelCount = 0;
                StringBuilder channelInfo = new StringBuilder();
                
                DecimalFormat df = new DecimalFormat("######0");
                for (Map<String, Object> item : results) {
                    // 只处理通道类型的节点 (nodeType = 3)
                    if (3 == Integer.valueOf(df.format(item.get("nodeType")))) {
                        channelCount++;
                        String channelName = (String) item.get("channelName");
                        String channelId = (String) item.get("channelId");
                        
                        if (channelInfo.length() > 0) {
                            channelInfo.append("; ");
                        }
                        channelInfo.append("📹 ").append(channelName).append("(").append(channelId).append(")");
                    }
                }
                
                if (channelCount > 0) {
                    System.out.println(indent + "   └─ 通道(" + channelCount + "个): " + channelInfo.toString());
                } else {
                    System.out.println(indent + "   └─ 通道: 无");
                }
            } else {
                System.out.println(indent + "   └─ 通道: 无");
            }
            
        } catch (Exception e) {
            System.out.println(indent + "   └─ 通道: 获取失败 (" + e.getMessage() + ")");
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        System.out.println("=".repeat(80));
        System.out.println("              大华开放平台 - 组织和通道信息获取工具 (HTTPS版)");
        System.out.println("=".repeat(80));
        System.out.println();
        
        try {
            getAllOrganizationsWithChannels();
        } catch (Exception e) {
            System.err.println("\n程序执行失败: " + e.getMessage());
            System.err.println("\n错误处理建议:");
            System.err.println("1. 确认服务器支持HTTPS协议");
            System.err.println("2. 检查端口配置是否正确");
            System.err.println("3. 重新获取Token (运行Login类)");
            System.err.println("4. 检查用户权限");
        }
        
        System.out.println("\n程序执行完成。");
    }
}
