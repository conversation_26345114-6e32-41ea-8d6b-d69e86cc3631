# 组织和通道信息获取工具

## 功能说明

本工具实现了以下功能：
1. 递归获取系统中的所有组织信息
2. 为每个组织获取其下属的所有通道信息
3. 以树状结构化的方式在控制台输出组织和通道信息
4. 提供完整的错误处理机制
5. 遵循项目现有的代码风格和架构模式

## 文件结构

```
com.dahua.demo.basic/
├── Channel.java                      # 通道数据模型类
├── Organization.java                 # 组织数据模型类
├── OrganizationChannelService.java   # 核心业务逻辑服务类
└── OrganizationChannelManager.java   # 主入口类
```

## 使用步骤

### 1. 配置服务器信息
确保 `src/main/resources/baseinfo.properties` 文件中配置了正确的服务器信息：
```properties
ip=你的服务器IP
port=你的服务器端口
userName=你的用户名
password=你的密码
```

### 2. 登录获取Token
运行登录程序获取有效的token：
```bash
java com.dahua.demo.login.Login
```

### 3. 运行组织通道获取工具
```bash
java com.dahua.demo.basic.OrganizationChannelManager
```

## 输出示例

```
================================================================================
                    组织和通道信息树状结构
================================================================================

📁 组织: 根组织
   ├─ 组织ID: ROOT_ORG_001
   ├─ 组织编码: ROOT
   ├─ 层级: 0
   ├─ 通道数量: 2
   ├─ 📹 通道: 大门监控
   │   ├─ 通道ID: CHANNEL_001
   │   ├─ 通道编码: CH001
   │   └─ 设备名称: 大门摄像头
   ├─ 📹 通道: 停车场监控
   │   ├─ 通道ID: CHANNEL_002
   │   ├─ 通道编码: CH002
   │   └─ 设备名称: 停车场摄像头
   └─ 子组织数量: 1
       └─ 
📁 组织: 一楼分区
   ├─ 组织ID: SUB_ORG_001
   ├─ 组织编码: FLOOR1
   ├─ 层级: 1
   ├─ 通道数量: 1
   ├─ 📹 通道: 大厅监控
   │   ├─ 通道ID: CHANNEL_003
   │   ├─ 通道编码: CH003
   │   └─ 设备名称: 大厅摄像头
   └─ 子组织数量: 0

================================================================================
📊 统计摘要:
   总组织数量: 2
   总通道数量: 3
   获取时间: Wed Jul 02 10:30:45 CST 2025
================================================================================
```

## 核心功能

### OrganizationChannelService 类
- `getAllOrganizationsWithChannels()`: 获取所有组织及其通道信息
- `printOrganizationTree()`: 打印树状结构的组织通道信息
- 完整的错误处理和异常管理

### 数据模型
- `Organization`: 组织信息模型，包含子组织和通道列表
- `Channel`: 通道信息模型，包含通道的详细属性

## 错误处理

工具包含以下错误处理机制：
1. Token验证和过期检查
2. 网络连接异常处理
3. API响应解析异常处理
4. 空数据和无效数据处理
5. 详细的错误信息输出和处理建议

## API调用说明

工具使用以下API端点：
- **端点**: `/videoService/devicesManager/deviceTree`
- **方法**: GET
- **参数**:
  - `id`: 组织ID（空字符串表示根组织）
  - `nodeType`: 1（表示组织）
  - `typeCode`: 
    - `"01"`: 查询组织
    - `"01;0;ALL;ALL"`: 查询通道
  - `page`: 页码
  - `pageSize`: 每页大小

## 注意事项

1. 确保网络连接正常
2. 确保用户有足够的权限访问组织和通道信息
3. Token有效期为2分钟，如果执行时间较长可能需要重新登录
4. 大型组织结构可能需要较长时间来获取完整信息

## 扩展功能

可以基于此工具进一步扩展：
1. 导出组织通道信息到文件（JSON、XML、Excel等）
2. 添加过滤和搜索功能
3. 集成到Web界面或GUI应用中
4. 添加实时监控和状态检查功能
