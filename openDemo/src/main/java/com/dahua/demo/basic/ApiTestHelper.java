package com.dahua.demo.basic;

import com.dahua.demo.util.BaseUserInfo;
import com.dahua.demo.util.HttpEnum;
import com.dahua.demo.util.HttpTestUtils;
import com.google.gson.Gson;

import java.util.Map;

/**
 * API测试辅助类
 * 用于测试和验证API连接、Token有效性等
 */
public class ApiTestHelper extends BaseUserInfo {
    
    private static final String ACTION = "/videoService/devicesManager/deviceTree";
    
    /**
     * 测试Token是否有效
     */
    public static boolean testToken() {
        System.out.println("=== Token有效性测试 ===");
        System.out.println("当前Token: " + (token != null ? token.substring(0, Math.min(20, token.length())) + "..." : "null"));
        
        if (token == null || token.trim().isEmpty()) {
            System.err.println("❌ Token为空，请先登录");
            return false;
        }
        
        try {
            // 尝试获取根组织信息来测试Token
            String content = "?id=&nodeType=1&typeCode=01&page=1&pageSize=1";
            String response = HttpTestUtils.httpRequest(HttpEnum.GET, ip, Integer.valueOf(port), ACTION, token, content);
            
            System.out.println("API原始响应: " + response);
            
            if (response == null || response.trim().isEmpty()) {
                System.err.println("❌ 收到空响应");
                return false;
            }
            
            // 检查是否为JSON格式
            try {
                Gson gson = new Gson();
                Map<String, Object> responseMap = gson.fromJson(response, Map.class);
                
                if (responseMap.containsKey("results")) {
                    System.out.println("✅ Token有效，API连接正常");
                    return true;
                } else if (responseMap.containsKey("error") || responseMap.containsKey("message")) {
                    String errorMsg = (String) responseMap.getOrDefault("message", responseMap.get("error"));
                    System.err.println("❌ API返回错误: " + errorMsg);
                    return false;
                } else {
                    System.err.println("❌ API响应格式异常: " + response);
                    return false;
                }
                
            } catch (Exception e) {
                System.err.println("❌ JSON解析失败，可能Token已过期或权限不足");
                System.err.println("响应内容: " + response);
                return false;
            }
            
        } catch (Exception e) {
            System.err.println("❌ API调用失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 测试网络连接
     */
    public static boolean testConnection() {
        System.out.println("\n=== 网络连接测试 ===");
        System.out.println("服务器地址: " + ip + ":" + port);
        
        try {
            // 尝试重新获取Token来测试连接
            String newToken = HttpTestUtils.getToken(ip, Integer.valueOf(port), userName, password);
            
            if (newToken != null && !newToken.trim().isEmpty()) {
                System.out.println("✅ 网络连接正常，成功获取新Token");
                System.out.println("新Token: " + newToken.substring(0, Math.min(20, newToken.length())) + "...");
                return true;
            } else {
                System.err.println("❌ 无法获取Token，请检查用户名密码");
                return false;
            }
            
        } catch (Exception e) {
            System.err.println("❌ 网络连接失败: " + e.getMessage());
            System.err.println("请检查:");
            System.err.println("1. 服务器地址是否正确: " + ip);
            System.err.println("2. 端口是否正确: " + port);
            System.err.println("3. 网络是否通畅");
            return false;
        }
    }
    
    /**
     * 运行完整的诊断测试
     */
    public static void runDiagnostics() {
        System.out.println("开始API诊断测试...\n");
        
        // 1. 测试网络连接
        boolean connectionOk = testConnection();
        
        // 2. 测试Token有效性
        boolean tokenOk = testToken();
        
        // 3. 输出诊断结果
        System.out.println("\n=== 诊断结果 ===");
        System.out.println("网络连接: " + (connectionOk ? "✅ 正常" : "❌ 异常"));
        System.out.println("Token状态: " + (tokenOk ? "✅ 有效" : "❌ 无效"));
        
        if (connectionOk && tokenOk) {
            System.out.println("\n🎉 所有测试通过，可以正常使用组织通道获取功能");
        } else {
            System.out.println("\n⚠️  存在问题，请根据上述提示进行修复");
            
            if (!connectionOk) {
                System.out.println("\n修复建议 - 网络连接问题:");
                System.out.println("1. 检查baseinfo.properties中的服务器配置");
                System.out.println("2. 确认服务器是否正常运行");
                System.out.println("3. 检查防火墙设置");
            }
            
            if (!tokenOk) {
                System.out.println("\n修复建议 - Token问题:");
                System.out.println("1. 运行Login类重新获取Token");
                System.out.println("2. 检查用户权限是否足够");
                System.out.println("3. 确认Token未过期（有效期2分钟）");
            }
        }
    }
    
    /**
     * 主方法 - 运行诊断测试
     */
    public static void main(String[] args) {
        System.out.println("=".repeat(60));
        System.out.println("           大华开放平台API诊断工具");
        System.out.println("=".repeat(60));
        
        runDiagnostics();
        
        System.out.println("\n诊断完成。");
    }
}
