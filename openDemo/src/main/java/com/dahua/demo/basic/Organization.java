package com.dahua.demo.basic;

import java.util.ArrayList;
import java.util.List;

/**
 * 组织信息数据模型
 * 用于存储从API获取的组织相关信息，包括子组织和通道列表
 */
public class Organization {
    private String id;
    private String orgName;
    private String orgCode;
    private int nodeType;
    private String parentId;
    private int level;
    private List<Organization> subOrganizations;
    private List<Channel> channels;
    
    public Organization() {
        this.subOrganizations = new ArrayList<>();
        this.channels = new ArrayList<>();
    }
    
    public Organization(String id, String orgName) {
        this();
        this.id = id;
        this.orgName = orgName;
    }
    
    // Getter and Setter methods
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getOrgName() {
        return orgName;
    }
    
    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
    
    public String getOrgCode() {
        return orgCode;
    }
    
    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }
    
    public int getNodeType() {
        return nodeType;
    }
    
    public void setNodeType(int nodeType) {
        this.nodeType = nodeType;
    }
    
    public String getParentId() {
        return parentId;
    }
    
    public void setParentId(String parentId) {
        this.parentId = parentId;
    }
    
    public int getLevel() {
        return level;
    }
    
    public void setLevel(int level) {
        this.level = level;
    }
    
    public List<Organization> getSubOrganizations() {
        return subOrganizations;
    }
    
    public void setSubOrganizations(List<Organization> subOrganizations) {
        this.subOrganizations = subOrganizations;
    }
    
    public void addSubOrganization(Organization organization) {
        this.subOrganizations.add(organization);
    }
    
    public List<Channel> getChannels() {
        return channels;
    }
    
    public void setChannels(List<Channel> channels) {
        this.channels = channels;
    }
    
    public void addChannel(Channel channel) {
        this.channels.add(channel);
    }
    
    @Override
    public String toString() {
        return "Organization{" +
                "id='" + id + '\'' +
                ", orgName='" + orgName + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", nodeType=" + nodeType +
                ", parentId='" + parentId + '\'' +
                ", level=" + level +
                ", subOrganizations=" + subOrganizations.size() +
                ", channels=" + channels.size() +
                '}';
    }
}
