package com.dahua.demo.basic;

import com.dahua.demo.login.Login;
import com.dahua.demo.util.HttpEnum;
import com.google.gson.Gson;
import org.apache.http.client.methods.*;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;

import javax.net.ssl.SSLContext;
import java.io.*;
import java.security.cert.X509Certificate;
import java.util.List;
import java.util.Map;

/**
 * HTTPS版本的HTTP工具类
 * 支持HTTPS协议和SSL证书忽略
 */
public class HttpsTestUtils {

    /**
     * 发送HTTPS请求
     */
    public static String httpsRequest(HttpEnum method, String ip, int port, String action, String token, String content) {
        String responJson = null;
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        InputStream inputStream = null;
        try {
            // 创建忽略SSL证书的HttpClient
            httpClient = createSSLHttpClient();
            String uri = "https://" + ip + ":" + port + action;
            HttpRequestBase httpReq = getRequestEntity(method, token, uri, content);
            httpResponse = httpClient.execute(httpReq);
            inputStream = httpResponse.getEntity().getContent();
            responJson = convertToString(inputStream);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != httpResponse) {
                try {
                    httpResponse.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (null != httpClient) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return responJson;
    }

    /**
     * 创建忽略SSL证书的HttpClient
     */
    private static CloseableHttpClient createSSLHttpClient() throws Exception {
        SSLContext sslContext = SSLContextBuilder.create()
                .loadTrustMaterial((X509Certificate[] chain, String authType) -> true)
                .build();
        
        SSLConnectionSocketFactory sslConnectionSocketFactory = new SSLConnectionSocketFactory(
                sslContext, 
                NoopHostnameVerifier.INSTANCE);
        
        return HttpClients.custom()
                .setSSLSocketFactory(sslConnectionSocketFactory)
                .build();
    }

    /**
     * 构建HTTP请求实体
     */
    private static HttpRequestBase getRequestEntity(HttpEnum method, String token, String uri, String content) throws UnsupportedEncodingException {
        switch(method.getNum()){
            case 1:
                HttpGet httpGet = new HttpGet(uri+content);
                httpGet.addHeader("Content-type", "application/json");
                httpGet.addHeader("X-Subject-Token", token);
                return httpGet;
            case 2:
                HttpPost httpPost = new HttpPost(uri);
                httpPost.addHeader("Content-type", "application/json");
                httpPost.addHeader("X-Subject-Token", token);
                StringEntity stringEntity = new StringEntity(content, "UTF-8");
                httpPost.setEntity(stringEntity);
                return httpPost;
            case 3:
                HttpPut httpPut = new HttpPut(uri);
                httpPut.addHeader("Content-type", "application/json");
                httpPut.addHeader("X-Subject-Token", token);
                StringEntity stringEntityPut = new StringEntity(content, "UTF-8");
                httpPut.setEntity(stringEntityPut);
                return httpPut;
            case 4:
                HttpDelete httpDelete = new HttpDelete(uri+content);
                httpDelete.addHeader("Content-type", "application/json");
                httpDelete.addHeader("X-Subject-Token", token);
                return httpDelete;
            default:
                return null;
        }
    }

    /**
     * 将InputStream转换为String
     */
    private static String convertToString(InputStream inputStream) {
        StringBuilder stringBuilder = new StringBuilder();
        BufferedReader bufferedReader = null;
        try {
            bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                stringBuilder.append(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 获取HTTPS Token
     */
    public static String getHttpsToken(String ip, int port, String userName, String password) throws Exception {
        String response = "";
        String token = "";
        response = httpsLogin(ip, port, userName, password);
        Map<String, Object> rsp = new Gson().fromJson(response, Map.class);
        String message = (String) rsp.get("message");
        if ("success".equals(message)) {
            Map<String, Object> params = (Map<String, Object>) rsp.get("params");
            token = (String) params.get("token");
        } else {
            throw new Exception("登录失败: " + message);
        }
        return token;
    }

    /**
     * HTTPS登录方法
     */
    public static String httpsLogin(String ip, int port, String userName, String password) throws Exception {
        // 第一次登录
        String firstResponse = httpsFirstLogin(ip, port, userName);
        Map<String, Object> firstRsp = new Gson().fromJson(firstResponse, Map.class);
        
        if (!"success".equals(firstRsp.get("message"))) {
            throw new Exception("第一次登录失败: " + firstRsp.get("message"));
        }
        
        Map<String, Object> params = (Map<String, Object>) firstRsp.get("params");
        String realm = (String) params.get("realm");
        String randomKey = (String) params.get("randomKey");
        
        // 第二次登录
        return httpsSecondLogin(ip, port, userName, password, realm, randomKey);
    }

    /**
     * HTTPS第一次登录
     */
    private static String httpsFirstLogin(String ip, int port, String userName) {
        Map<String, Object> loginFirst = new java.util.HashMap<>();
        loginFirst.put("clientType", "winpc");
        loginFirst.put("userName", userName);
        return httpsRequest(HttpEnum.POST, ip, port, "/evo-apigw/evo-oauth/1.0.0/oauth/token", "", new Gson().toJson(loginFirst));
    }

    /**
     * HTTPS第二次登录
     */
    private static String httpsSecondLogin(String ip, int port, String userName, String password, String realm, String randomKey) throws Exception {
        Map<String, Object> loginSecond = new java.util.HashMap<>();
        loginSecond.put("clientType", "winpc");
        loginSecond.put("userName", userName);
        loginSecond.put("randomKey", randomKey);
        loginSecond.put("encryptType", "MD5");
        
        // 计算签名 (简化版本，实际应该使用LoginSecond类的calcSignature方法)
        String signature = calculateSignature(password, realm, randomKey);
        loginSecond.put("signature", signature);
        
        return httpsRequest(HttpEnum.POST, ip, port, "/evo-apigw/evo-oauth/1.0.0/oauth/token", "", new Gson().toJson(loginSecond));
    }

    /**
     * 计算签名 (简化版本)
     */
    private static String calculateSignature(String password, String realm, String randomKey) throws Exception {
        // 这里应该实现与LoginSecond.calcSignature相同的逻辑
        // 为了简化，这里返回一个占位符
        // 实际使用时需要实现正确的MD5签名算法
        java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
        String input = userName + ":" + realm + ":" + password;
        byte[] hash = md.digest(input.getBytes("UTF-8"));
        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
}
