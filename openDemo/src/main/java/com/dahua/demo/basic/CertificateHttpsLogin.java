package com.dahua.demo.basic;

import com.dahua.demo.util.BaseUserInfo;
import com.google.gson.Gson;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLSession;
import java.io.*;
import java.net.URL;
import java.security.KeyStore;
import java.security.MessageDigest;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.util.HashMap;
import java.util.Map;

/**
 * 使用真实SSL证书的HTTPS登录工具
 * 基于Java内置的HttpsURLConnection，支持大华平台证书
 */
public class CertificateHttpsLogin extends BaseUserInfo {
    
    private static final String LOGIN_ACTION = "/videoService/accounts/authorize";
    private static SSLContext sslContext = null;
    
    /**
     * 初始化SSL上下文，加载证书
     */
    private static synchronized void initSSLContext() throws Exception {
        if (sslContext != null) {
            return;
        }
        
        System.out.println("正在加载SSL证书...");
        
        // 创建证书工厂
        CertificateFactory cf = CertificateFactory.getInstance("X.509");
        
        // 加载根证书
        InputStream rootCertStream = CertificateHttpsLogin.class.getClassLoader()
                .getResourceAsStream("rootChain.crt");
        if (rootCertStream == null) {
            throw new Exception("找不到根证书文件: rootChain.crt");
        }
        Certificate rootCert = cf.generateCertificate(rootCertStream);
        rootCertStream.close();
        System.out.println("✅ 根证书加载成功");
        
        // 加载服务器证书
        InputStream serverCertStream = CertificateHttpsLogin.class.getClassLoader()
                .getResourceAsStream("key.cer");
        if (serverCertStream == null) {
            throw new Exception("找不到服务器证书文件: key.cer");
        }
        Certificate serverCert = cf.generateCertificate(serverCertStream);
        serverCertStream.close();
        System.out.println("✅ 服务器证书加载成功");
        
        // 创建KeyStore并添加证书
        KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
        keyStore.load(null, null);
        keyStore.setCertificateEntry("rootCA", rootCert);
        keyStore.setCertificateEntry("serverCert", serverCert);
        System.out.println("✅ 证书已添加到KeyStore");
        
        // 创建TrustManager
        TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        tmf.init(keyStore);
        
        // 创建SSL上下文
        sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, tmf.getTrustManagers(), null);
        System.out.println("✅ SSL上下文初始化完成");
    }
    
    /**
     * 发送安全HTTPS POST请求
     */
    private static String sendSecureHttpsPost(String jsonData) {
        try {
            initSSLContext();
            
            // 构建HTTPS URL
            String httpsUrl = "https://" + ip + ":" + port + LOGIN_ACTION;
            URL url = new URL(httpsUrl);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            
            // 设置SSL上下文
            connection.setSSLSocketFactory(sslContext.getSocketFactory());

            // 设置主机名验证器（允许IP地址）
            connection.setHostnameVerifier(new HostnameVerifier() {
                @Override
                public boolean verify(String hostname, SSLSession session) {
                    // 允许IP地址连接，因为证书可能只包含域名
                    System.out.println("验证主机名: " + hostname);
                    return true; // 在生产环境中应该进行适当的验证
                }
            });
            
            // 设置请求头
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setDoOutput(true);
            connection.setConnectTimeout(10000); // 10秒连接超时
            connection.setReadTimeout(30000);    // 30秒读取超时
            
            // 发送POST数据
            OutputStream os = connection.getOutputStream();
            os.write(jsonData.getBytes("UTF-8"));
            os.flush();
            os.close();
            
            // 检查响应码
            int responseCode = connection.getResponseCode();
            System.out.println("HTTP响应码: " + responseCode);
            
            // 读取响应
            InputStream inputStream;
            if (responseCode >= 200 && responseCode < 300) {
                inputStream = connection.getInputStream();
            } else if (responseCode == 401) {
                // 401对于第一次登录是正常的，服务器返回realm和randomKey
                inputStream = connection.getErrorStream();
                if (inputStream == null) {
                    inputStream = connection.getInputStream();
                }
            } else {
                inputStream = connection.getErrorStream();
            }
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();
            
            return response.toString();
            
        } catch (Exception e) {
            System.err.println("安全HTTPS登录请求失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 第一次登录
     */
    private static String firstLogin() {
        Map<String, Object> loginFirst = new HashMap<>();
        loginFirst.put("clientType", "winpc");
        loginFirst.put("userName", userName);
        
        String jsonData = new Gson().toJson(loginFirst);
        System.out.println("第一次登录请求: " + jsonData);
        
        return sendSecureHttpsPost(jsonData);
    }
    
    /**
     * 第二次登录
     */
    private static String secondLogin(String realm, String randomKey) throws Exception {
        Map<String, Object> loginSecond = new HashMap<>();
        loginSecond.put("clientType", "winpc");
        loginSecond.put("userName", userName);
        loginSecond.put("randomKey", randomKey);
        loginSecond.put("encryptType", "MD5");
        
        // 计算签名 - 使用与LoginSecond.calcSignature相同的算法
        String signature = calculateSignature(password, realm, randomKey);
        loginSecond.put("signature", signature);
        
        String jsonData = new Gson().toJson(loginSecond);
        System.out.println("第二次登录请求: " + jsonData);
        
        return sendSecureHttpsPost(jsonData);
    }
    
    /**
     * 计算MD5签名 - 使用与LoginSecond.calcSignature相同的算法
     */
    private static String calculateSignature(String password, String realm, String randomKey) throws Exception {
        System.out.println("签名计算过程:");
        
        // 步骤1: MD5(password)
        String step1 = md5(password);
        System.out.println("  步骤1 - MD5(password): " + step1);
        
        // 步骤2: MD5(userName + step1)
        String step2 = md5(userName + step1);
        System.out.println("  步骤2 - MD5(userName + step1): " + step2);
        
        // 步骤3: MD5(step2)
        String step3 = md5(step2);
        System.out.println("  步骤3 - MD5(step2): " + step3);
        
        // 步骤4: MD5(userName + ":" + realm + ":" + step3)
        String step4 = md5(userName + ":" + realm + ":" + step3);
        System.out.println("  步骤4 - MD5(userName:realm:step3): " + step4);
        
        // 步骤5: MD5(step4 + ":" + randomKey)
        String signature = md5(step4 + ":" + randomKey);
        System.out.println("  最终签名: " + signature);
        
        return signature;
    }
    
    /**
     * MD5加密
     */
    private static String md5(String input) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] hash = md.digest(input.getBytes("UTF-8"));
        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
    
    /**
     * 执行安全HTTPS登录
     */
    public static String certificateLogin() throws Exception {
        System.out.println("=".repeat(80));
        System.out.println("          大华开放平台 - 安全SSL证书登录工具");
        System.out.println("=".repeat(80));
        System.out.println("服务器地址: " + ip + ":" + port + " (安全HTTPS)");
        System.out.println("用户名: " + userName);
        System.out.println("密码: " + password.replaceAll(".", "*"));
        System.out.println("使用证书: rootChain.crt + key.cer");
        System.out.println();
        
        // 第一次登录
        System.out.println("步骤1: 发送第一次登录请求...");
        String firstResponse = firstLogin();
        
        if (firstResponse == null || firstResponse.trim().isEmpty()) {
            throw new Exception("第一次登录失败: 收到空响应");
        }
        
        if (!firstResponse.startsWith("{")) {
            throw new Exception("第一次登录失败: " + firstResponse);
        }
        
        System.out.println("第一次登录响应: " + firstResponse);
        
        Map<String, Object> firstRsp = new Gson().fromJson(firstResponse, Map.class);

        // 第一次登录的响应直接包含realm和randomKey，没有message字段
        String realm = (String) firstRsp.get("realm");
        String randomKey = (String) firstRsp.get("randomKey");

        if (realm == null || randomKey == null) {
            throw new Exception("第一次登录失败: 未获取到realm或randomKey");
        }
        
        System.out.println("✅ 第一次登录成功");
        System.out.println("Realm: " + realm);
        System.out.println("RandomKey: " + randomKey);
        System.out.println();
        
        // 第二次登录
        System.out.println("步骤2: 发送第二次登录请求...");
        String secondResponse = secondLogin(realm, randomKey);
        
        if (secondResponse == null || secondResponse.trim().isEmpty()) {
            throw new Exception("第二次登录失败: 收到空响应");
        }
        
        if (!secondResponse.startsWith("{")) {
            throw new Exception("第二次登录失败: " + secondResponse);
        }
        
        System.out.println("第二次登录响应: " + secondResponse);
        
        Map<String, Object> secondRsp = new Gson().fromJson(secondResponse, Map.class);

        // 第二次登录成功后，token直接在响应中
        String token = (String) secondRsp.get("token");

        if (token == null || token.trim().isEmpty()) {
            throw new Exception("第二次登录失败: 未获取到token");
        }
        
        System.out.println("✅ 第二次登录成功");
        System.out.println("Token: " + token);
        System.out.println();
        
        return token;
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        try {
            String newToken = certificateLogin();
            
            System.out.println("=".repeat(80));
            System.out.println("🎉 安全SSL证书登录成功！");
            System.out.println("新Token已获取: " + newToken.substring(0, Math.min(30, newToken.length())) + "...");
            System.out.println("Token长度: " + newToken.length() + " 字符");
            System.out.println();
            System.out.println("💡 提示:");
            System.out.println("1. Token有效期为2分钟，请立即使用");
            System.out.println("2. 现在可以运行组织通道查询程序");
            System.out.println("3. SSL证书验证已通过，连接安全");
            System.out.println("=".repeat(80));
            
        } catch (Exception e) {
            System.err.println("\n❌ 安全登录失败: " + e.getMessage());
            System.err.println("\n错误处理建议:");
            System.err.println("1. 检查SSL证书文件是否存在且有效");
            System.err.println("2. 确认服务器支持提供的证书");
            System.err.println("3. 检查用户名和密码是否正确");
            System.err.println("4. 确认服务器地址和端口");
            System.err.println("5. 检查网络连接");
            e.printStackTrace();
        }
    }
}
