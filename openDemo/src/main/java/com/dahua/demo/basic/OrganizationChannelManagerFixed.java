package com.dahua.demo.basic;

import com.dahua.demo.util.BaseUserInfo;
import com.dahua.demo.util.HttpEnum;
import com.dahua.demo.util.HttpTestUtils;
import com.google.gson.Gson;

import java.text.DecimalFormat;
import java.util.List;
import java.util.Map;

/**
 * 组织和通道管理器 - 修复版本
 * 基于现有OrgTree和DevInfo的实现模式，修复JSON解析问题
 */
public class OrganizationChannelManagerFixed extends BaseUserInfo {
    
    public static final String ACTION = "/videoService/devicesManager/deviceTree";
    private static int orgLevel = -1; // 组织层级标志
    private static String indentMark = ""; // 打印结果的缩进标志
    
    /**
     * 获取并打印所有组织和通道信息
     */
    public static void getAllOrganizationsWithChannels() throws Exception {
        System.out.println("开始获取组织和通道信息...");
        System.out.println("服务器地址: " + ip + ":" + port);
        System.out.println("Token: " + (token != null ? token.substring(0, Math.min(20, token.length())) + "..." : "null"));
        System.out.println();
        
        // 检查Token
        if (token == null || token.trim().isEmpty()) {
            System.err.println("错误: Token为空，请先运行Login类进行登录");
            return;
        }
        
        System.out.println("=".repeat(80));
        System.out.println("                    组织和通道信息树状结构");
        System.out.println("=".repeat(80));
        
        // 从根组织开始递归获取
        getSubOrganizations("");
        
        System.out.println("=".repeat(80));
        System.out.println("获取完成！");
    }
    
    /**
     * 递归获取子组织和通道信息
     * @param parentOrgId 父组织ID，空字符串表示根组织
     */
    private static void getSubOrganizations(String parentOrgId) throws Exception {
        try {
            // 构建查询组织的参数
            String content = "?id=" + parentOrgId + "&nodeType=1&typeCode=01&page=1&pageSize=100";
            String response = HttpTestUtils.httpRequest(HttpEnum.GET, ip, Integer.valueOf(port), ACTION, token, content);
            
            // 检查响应是否为空
            if (response == null || response.trim().isEmpty()) {
                System.err.println("收到空响应，可能Token已过期或网络异常");
                return;
            }
            
            // 检查是否为错误消息
            if (!response.startsWith("{")) {
                System.err.println("API返回错误信息: " + response);
                if (response.contains("token") || response.contains("过期") || response.contains("expire")) {
                    System.err.println("Token可能已过期，请重新登录");
                }
                return;
            }
            
            // 解析JSON响应
            Map<String, Object> responseMap = new Gson().fromJson(response, Map.class);
            
            // 检查是否有错误信息
            if (responseMap.containsKey("error") || responseMap.containsKey("message")) {
                String errorMsg = (String) responseMap.getOrDefault("message", responseMap.get("error"));
                System.err.println("API返回错误: " + errorMsg);
                return;
            }
            
            List<Map<String, Object>> results = (List<Map<String, Object>>) responseMap.get("results");
            
            if (results != null && results.size() > 0) {
                for (Map<String, Object> orgNode : results) {
                    orgLevel++;
                    indentMark += "   ";
                    
                    // 打印组织信息
                    String orgName = (String) orgNode.get("orgName");
                    String orgId = (String) orgNode.get("id");
                    System.out.println(indentMark + "📁 " + orgLevel + "级组织: " + orgName + " (ID: " + orgId + ")");
                    
                    // 获取该组织下的通道信息
                    getChannelsForOrganization(orgId, indentMark);
                    
                    // 递归获取子组织
                    getSubOrganizations(orgId);
                    
                    // 恢复缩进级别
                    indentMark = indentMark.substring(0, indentMark.length() - 3);
                    orgLevel--;
                }
            } else {
                if (parentOrgId.isEmpty()) {
                    System.out.println("未找到任何组织信息，可能是权限不足或系统中没有数据");
                }
            }
            
        } catch (Exception e) {
            System.err.println("获取组织信息时发生错误: " + e.getMessage());
            if (e.getMessage().contains("JsonSyntaxException") || e.getMessage().contains("Expected BEGIN_OBJECT")) {
                System.err.println("JSON解析错误，可能是Token过期或API返回了错误信息");
                System.err.println("建议: 请重新运行Login类获取新的Token");
            }
            throw e;
        }
    }
    
    /**
     * 获取指定组织下的通道信息
     * @param orgId 组织ID
     * @param indent 缩进字符串
     */
    private static void getChannelsForOrganization(String orgId, String indent) {
        try {
            // 构建查询通道的参数
            String content = "?id=" + orgId + "&nodeType=1&typeCode=01;0;ALL;ALL&page=1&pageSize=100";
            String response = HttpTestUtils.httpRequest(HttpEnum.GET, ip, Integer.valueOf(port), ACTION, token, content);
            
            if (response == null || response.trim().isEmpty()) {
                return; // 静默返回，不打印错误
            }
            
            // 检查是否为错误消息
            if (!response.startsWith("{")) {
                return; // 静默返回，不打印错误
            }
            
            Map<String, Object> responseMap = new Gson().fromJson(response, Map.class);
            List<Map<String, Object>> results = (List<Map<String, Object>>) responseMap.get("results");
            
            if (results != null && results.size() > 0) {
                int channelCount = 0;
                StringBuilder channelInfo = new StringBuilder();
                
                DecimalFormat df = new DecimalFormat("######0");
                for (Map<String, Object> item : results) {
                    // 只处理通道类型的节点 (nodeType = 3)
                    if (3 == Integer.valueOf(df.format(item.get("nodeType")))) {
                        channelCount++;
                        String channelName = (String) item.get("channelName");
                        String channelId = (String) item.get("channelId");
                        
                        if (channelInfo.length() > 0) {
                            channelInfo.append("; ");
                        }
                        channelInfo.append("📹 ").append(channelName).append("(").append(channelId).append(")");
                    }
                }
                
                if (channelCount > 0) {
                    System.out.println(indent + "   └─ 通道(" + channelCount + "个): " + channelInfo.toString());
                } else {
                    System.out.println(indent + "   └─ 通道: 无");
                }
            } else {
                System.out.println(indent + "   └─ 通道: 无");
            }
            
        } catch (Exception e) {
            // 静默处理通道获取错误，不影响组织信息的获取
            System.out.println(indent + "   └─ 通道: 获取失败");
        }
    }
    
    /**
     * 测试Token有效性
     */
    public static boolean testTokenValidity() {
        try {
            System.out.println("正在测试Token有效性...");
            String content = "?id=&nodeType=1&typeCode=01&page=1&pageSize=1";
            String response = HttpTestUtils.httpRequest(HttpEnum.GET, ip, Integer.valueOf(port), ACTION, token, content);
            
            if (response == null || response.trim().isEmpty()) {
                System.err.println("❌ 收到空响应");
                return false;
            }
            
            if (!response.startsWith("{")) {
                System.err.println("❌ API返回非JSON响应: " + response);
                return false;
            }
            
            Map<String, Object> responseMap = new Gson().fromJson(response, Map.class);
            if (responseMap.containsKey("results")) {
                System.out.println("✅ Token有效");
                return true;
            } else {
                System.err.println("❌ Token无效或权限不足");
                return false;
            }
            
        } catch (Exception e) {
            System.err.println("❌ Token测试失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        System.out.println("=".repeat(80));
        System.out.println("              大华开放平台 - 组织和通道信息获取工具 (修复版)");
        System.out.println("=".repeat(80));
        System.out.println();
        
        try {
            // 先测试Token有效性
            if (!testTokenValidity()) {
                System.err.println("\n请先解决Token问题:");
                System.err.println("1. 运行 com.dahua.demo.login.Login 重新登录");
                System.err.println("2. 确保用户有足够的权限");
                System.err.println("3. 检查网络连接是否正常");
                return;
            }
            
            // 获取并显示组织通道信息
            getAllOrganizationsWithChannels();
            
        } catch (Exception e) {
            System.err.println("\n程序执行失败: " + e.getMessage());
            System.err.println("\n错误处理建议:");
            System.err.println("1. 检查Token是否过期 (有效期2分钟)");
            System.err.println("2. 重新运行Login类获取新Token");
            System.err.println("3. 检查网络连接和服务器状态");
            System.err.println("4. 确认用户权限是否足够");
        }
        
        System.out.println("\n程序执行完成。");
    }
}
