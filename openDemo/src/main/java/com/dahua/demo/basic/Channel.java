package com.dahua.demo.basic;

/**
 * 通道信息数据模型
 * 用于存储从API获取的通道相关信息
 */
public class Channel {
    private String channelId;
    private String channelName;
    private String channelCode;
    private int nodeType;
    private String deviceId;
    private String deviceName;
    private String status;
    
    public Channel() {
    }
    
    public Channel(String channelId, String channelName) {
        this.channelId = channelId;
        this.channelName = channelName;
    }
    
    // Getter and Setter methods
    public String getChannelId() {
        return channelId;
    }
    
    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }
    
    public String getChannelName() {
        return channelName;
    }
    
    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }
    
    public String getChannelCode() {
        return channelCode;
    }
    
    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }
    
    public int getNodeType() {
        return nodeType;
    }
    
    public void setNodeType(int nodeType) {
        this.nodeType = nodeType;
    }
    
    public String getDeviceId() {
        return deviceId;
    }
    
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
    
    public String getDeviceName() {
        return deviceName;
    }
    
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    @Override
    public String toString() {
        return "Channel{" +
                "channelId='" + channelId + '\'' +
                ", channelName='" + channelName + '\'' +
                ", channelCode='" + channelCode + '\'' +
                ", nodeType=" + nodeType +
                ", deviceId='" + deviceId + '\'' +
                ", deviceName='" + deviceName + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
